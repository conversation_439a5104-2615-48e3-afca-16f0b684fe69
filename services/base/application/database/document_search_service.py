from abc import ABC, abstractmethod
from typing import Optional, Sequence

from services.base.application.boundaries.documents import (
    SearchUniqueDocumentsOutputBoundary,
)
from services.base.application.database.models.filters import Filters
from services.base.application.database.models.sorts import Sort
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery


class DocumentSearchService(ABC):
    @abstractmethod
    def __init__(self, **kwargs):
        pass

    @abstractmethod
    async def close(self):
        pass

    @abstractmethod
    async def search_documents_by_query(
        self,
        sorts: Sequence[Sort],
        query: Query,
        size: int = 1000,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[Document]:
        pass

    @abstractmethod
    async def map_documents_to[T: Document](
        self,
        sorts: Sequence[Sort],
        projected_type: type[T],
        query: Query,
        size: int = 1000,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[T]:
        pass

    @abstractmethod
    async def search_documents_by_single_query[T: Document](
        self,
        sorts: Sequence[Sort],
        query: SingleDocumentTypeQuery[T],
        size: int = 1000,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[T]:
        pass

    @abstractmethod
    async def search_unique_by_query[T: Document](
        self,
        data_schema: type[T],
        size: int,
        field_name: str,
        filters: Optional[Filters] = None,
        sorts: Optional[Sequence[Sort]] = None,
    ) -> Sequence[SearchUniqueDocumentsOutputBoundary[T]]:
        pass

    @abstractmethod
    async def count_by_query(self, query: Query) -> int:
        pass

    @abstractmethod
    async def count_unique_by_query(self, query: Query, field_name: str) -> int:
        pass
